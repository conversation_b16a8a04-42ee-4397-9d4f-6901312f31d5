@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Create Assignment"
        description="Set up a new assignment for students"
        :back-route="route('admin.grading.assignments.index')"
        back-label="Back to Assignments">
    </x-page-header>

    <!-- Assignment Form -->
    <div class="card">
        <form method="POST" action="{{ route('admin.grading.assignments.store') }}" class="space-y-6">
            @csrf
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-1">Assignment Title *</label>
                    <input type="text" name="title" id="title"
                           class="form-input"
                           value="{{ old('title') }}"
                           placeholder="e.g., Math Homework Chapter 5" required>
                    @error('title')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Assignment Code -->
                <div>
                    <label for="assignment_code" class="block text-sm font-medium text-gray-700 mb-1">Assignment Code *</label>
                    <input type="text" name="assignment_code" id="assignment_code"
                           class="form-input"
                           value="{{ old('assignment_code') }}"
                           placeholder="e.g., MATH001" required>
                    @error('assignment_code')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Academic Year -->
                <div>
                    <label for="academic_year_id" class="block text-sm font-medium text-gray-700 mb-1">Academic Year *</label>
                    <select name="academic_year_id" id="academic_year_id" class="form-select" required>
                        <option value="">Select Academic Year</option>
                        @foreach($academicYears ?? [] as $year)
                            <option value="{{ $year->id }}" {{ old('academic_year_id') == $year->id ? 'selected' : '' }}>
                                {{ $year->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('academic_year_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Academic Term -->
                <div>
                    <label for="academic_term_id" class="block text-sm font-medium text-gray-700 mb-1">Academic Term *</label>
                    <select name="academic_term_id" id="academic_term_id" class="form-select" required>
                        <option value="">Select Academic Term</option>
                        @foreach($academicTerms ?? [] as $term)
                            <option value="{{ $term->id }}" {{ old('academic_term_id') == $term->id ? 'selected' : '' }}>
                                {{ $term->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('academic_term_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Grade Category -->
                <div>
                    <label for="grade_category_id" class="block text-sm font-medium text-gray-700 mb-1">Grade Category *</label>
                    <select name="grade_category_id" id="grade_category_id" class="form-select" required>
                        <option value="">Select Grade Category</option>
                        @foreach($gradeCategories ?? [] as $category)
                            <option value="{{ $category->id }}" {{ old('grade_category_id') == $category->id ? 'selected' : '' }}>
                                {{ $category->name }} ({{ $category->weight }}%)
                            </option>
                        @endforeach
                    </select>
                    @error('grade_category_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Subject -->
                <div>
                    <label for="subject_id" class="block text-sm font-medium text-gray-700 mb-1">Subject *</label>
                    <select name="subject_id" id="subject_id" class="form-select" required>
                        <option value="">Select Subject</option>
                        @foreach($subjects ?? [] as $subject)
                            <option value="{{ $subject->id }}" {{ old('subject_id') == $subject->id ? 'selected' : '' }}>
                                {{ $subject->name }} ({{ $subject->subject_code }})
                            </option>
                        @endforeach
                    </select>
                    @error('subject_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Class -->
                <div>
                    <label for="class_id" class="block text-sm font-medium text-gray-700 mb-1">Class *</label>
                    <select name="class_id" id="class_id" class="form-select" required>
                        <option value="">Select Class</option>
                        @foreach($classRooms ?? [] as $class)
                            <option value="{{ $class->id }}" {{ old('class_id') == $class->id ? 'selected' : '' }}>
                                {{ $class->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('class_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Total Marks -->
                <div>
                    <label for="total_marks" class="block text-sm font-medium text-gray-700 mb-1">Total Marks *</label>
                    <input type="number" name="total_marks" id="total_marks"
                           class="form-input"
                           value="{{ old('total_marks') }}"
                           min="0" step="0.1" required>
                    @error('total_marks')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Passing Marks -->
                <div>
                    <label for="passing_marks" class="block text-sm font-medium text-gray-700 mb-1">Passing Marks *</label>
                    <input type="number" name="passing_marks" id="passing_marks"
                           class="form-input"
                           value="{{ old('passing_marks') }}"
                           min="0" step="0.1" required>
                    @error('passing_marks')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Assigned Date -->
                <div>
                    <label for="assigned_date" class="block text-sm font-medium text-gray-700 mb-1">Assigned Date *</label>
                    <input type="date" name="assigned_date" id="assigned_date"
                           class="form-input"
                           value="{{ old('assigned_date') }}" required>
                    @error('assigned_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Due Date -->
                <div>
                    <label for="due_date" class="block text-sm font-medium text-gray-700 mb-1">Due Date *</label>
                    <input type="date" name="due_date" id="due_date"
                           class="form-input"
                           value="{{ old('due_date') }}" required>
                    @error('due_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status *</label>
                    <select name="status" id="status" class="form-select" required>
                        <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>Draft</option>
                        <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
                        <option value="completed" {{ old('status') == 'completed' ? 'selected' : '' }}>Completed</option>
                        <option value="cancelled" {{ old('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                    </select>
                    @error('status')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Submission Type -->
                <div>
                    <label for="submission_type" class="block text-sm font-medium text-gray-700 mb-1">Submission Type *</label>
                    <select name="submission_type" id="submission_type" class="form-select" required>
                        <option value="">Select Type</option>
                        <option value="online" {{ old('submission_type') == 'online' ? 'selected' : '' }}>Online</option>
                        <option value="offline" {{ old('submission_type') == 'offline' ? 'selected' : '' }}>Offline</option>
                        <option value="both" {{ old('submission_type') == 'both' ? 'selected' : '' }}>Both</option>
                    </select>
                    @error('submission_type')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea name="description" id="description" rows="3"
                          class="form-textarea"
                          placeholder="Brief description of the assignment">{{ old('description') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Instructions -->
            <div>
                <label for="instructions" class="block text-sm font-medium text-gray-700 mb-1">Instructions</label>
                <textarea name="instructions" id="instructions" rows="4"
                          class="form-textarea"
                          placeholder="Detailed instructions for students">{{ old('instructions') }}</textarea>
                @error('instructions')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Additional Fields -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Late Penalty Percentage -->
                <div>
                    <label for="late_penalty_percentage" class="block text-sm font-medium text-gray-700 mb-1">Late Penalty (%)</label>
                    <input type="number" name="late_penalty_percentage" id="late_penalty_percentage"
                           class="form-input"
                           value="{{ old('late_penalty_percentage', 0) }}"
                           min="0" max="100" step="1">
                    @error('late_penalty_percentage')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Max Attempts -->
                <div>
                    <label for="max_attempts" class="block text-sm font-medium text-gray-700 mb-1">Max Attempts</label>
                    <input type="number" name="max_attempts" id="max_attempts"
                           class="form-input"
                           value="{{ old('max_attempts', 1) }}"
                           min="1" step="1">
                    @error('max_attempts')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Checkboxes -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="flex items-center">
                    <input type="checkbox" name="allow_late_submission" id="allow_late_submission"
                           class="form-checkbox" value="1" {{ old('allow_late_submission') ? 'checked' : '' }}>
                    <label for="allow_late_submission" class="ml-2 text-sm text-gray-700">
                        Allow late submission
                    </label>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" name="is_published" id="is_published"
                           class="form-checkbox" value="1" {{ old('is_published') ? 'checked' : '' }}>
                    <label for="is_published" class="ml-2 text-sm text-gray-700">
                        Publish assignment (students will be able to see this assignment)
                    </label>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.grading.assignments.index') }}" class="btn-cancel">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Create Assignment
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
