<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Assignment;
use App\Models\AcademicYear;
use App\Models\AcademicTerm;
use App\Models\GradeCategory;
use App\Models\GradeScale;
use App\Models\Subject;
use App\Models\ClassModel;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AssignmentController extends Controller
{
    public function index(Request $request)
    {
        $query = Assignment::with(['academicYear', 'academicTerm', 'gradeCategory', 'subject', 'classRoom']);

        // Apply filters
        if ($request->filled('academic_year_id')) {
            $query->where('academic_year_id', $request->academic_year_id);
        }

        if ($request->filled('academic_term_id')) {
            $query->where('academic_term_id', $request->academic_term_id);
        }

        if ($request->filled('grade_category_id')) {
            $query->where('grade_category_id', $request->grade_category_id);
        }

        if ($request->filled('subject_id')) {
            $query->where('subject_id', $request->subject_id);
        }

        if ($request->filled('class_id')) {
            $query->where('class_id', $request->class_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('assignment_code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $assignments = $query->orderBy('due_date', 'desc')->paginate(15);

        // Statistics
        $stats = [
            'total' => Assignment::count(),
            'active' => Assignment::where('status', 'active')->count(),
            'draft' => Assignment::where('status', 'draft')->count(),
            'completed' => Assignment::where('status', 'completed')->count(),
            'overdue' => Assignment::where('due_date', '<', now())->where('status', '!=', 'completed')->count(),
        ];

        // Filter options
        $academicYears = AcademicYear::orderBy('name')->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        $gradeCategories = GradeCategory::where('is_active', true)->orderBy('name')->get();
        $subjects = Subject::where('is_active', true)->orderBy('name')->get();
        $classRooms = ClassModel::where('is_active', true)->orderBy('name')->get();

        return view('admin.assignments.index', compact(
            'assignments',
            'stats',
            'academicYears',
            'academicTerms',
            'gradeCategories',
            'subjects',
            'classRooms'
        ));
    }

    public function create()
    {
        $academicYears = AcademicYear::orderBy('name')->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        $gradeCategories = GradeCategory::where('is_active', true)->orderBy('name')->get();
        $subjects = Subject::where('is_active', true)->orderBy('name')->get();
        $classRooms = ClassModel::where('is_active', true)->orderBy('name')->get();
        $gradeScales = GradeScale::where('is_active', true)->orderBy('name')->get();
        $teachers = User::whereHas('teacher')->where('is_active', true)->orderBy('name')->get();

        return view('admin.assignments.create', compact(
            'academicYears',
            'academicTerms',
            'gradeCategories',
            'subjects',
            'classRooms',
            'gradeScales',
            'teachers'
        ));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'assignment_code' => 'required|string|max:50|unique:assignments',
            'description' => 'nullable|string',
            'instructions' => 'nullable|string',
            'academic_year_id' => 'required|exists:academic_years,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'grade_category_id' => 'required|exists:grade_categories,id',
            'subject_id' => 'required|exists:subjects,id',
            'class_id' => 'required|exists:classes,id',
            'total_marks' => 'required|numeric|min:0',
            'passing_marks' => 'required|numeric|min:0|lte:total_marks',
            'assigned_date' => 'required|date',
            'due_date' => 'required|date|after_or_equal:assigned_date',
            'submission_type' => 'required|in:online,offline,both',
            'allow_late_submission' => 'boolean',
            'late_penalty_percentage' => 'nullable|numeric|min:0|max:100',
            'max_attempts' => 'nullable|integer|min:1',
            'is_published' => 'boolean',
            'status' => 'required|in:draft,active,completed,cancelled',
        ]);

        $assignment = Assignment::create($validated);

        return redirect()->route('admin.grading.assignments.show', $assignment)
            ->with('success', 'Assignment created successfully.');
    }

    public function show(Assignment $assignment)
    {
        $assignment->load([
            'academicYear',
            'academicTerm',
            'gradeCategory',
            'subject',
            'classRoom',
            'creator',
            'teacher',
            'submissions.student.user',
            'submissions.grade'
        ]);

        // Get submission statistics
        $submissionStats = [
            'total_students' => $assignment->classRoom->students()->count(),
            'submitted' => $assignment->submissions()->count(),
            'pending' => $assignment->classRoom->students()->count() - $assignment->submissions()->count(),
            'graded' => $assignment->submissions()->whereHas('grade')->count(),
            'average_marks' => $assignment->submissions()->whereHas('grade')->with('grade')->get()->avg('grade.marks_obtained'),
        ];

        return view('admin.assignments.show', compact('assignment', 'submissionStats'));
    }

    public function submissions(Assignment $assignment)
    {
        $assignment->load(['academicYear', 'academicTerm', 'gradeCategory', 'subject', 'classRoom']);

        $submissions = $assignment->submissions()
            ->with(['student.user', 'grade'])
            ->orderBy('submitted_at', 'desc')
            ->paginate(20);

        return view('admin.assignments.submissions', compact('assignment', 'submissions'));
    }

    public function edit(Assignment $assignment)
    {
        $academicYears = AcademicYear::orderBy('name')->get();
        $academicTerms = AcademicTerm::orderBy('name')->get();
        $gradeCategories = GradeCategory::where('is_active', true)->orderBy('name')->get();
        $subjects = Subject::where('is_active', true)->orderBy('name')->get();
        $classRooms = ClassModel::where('is_active', true)->orderBy('name')->get();
        $gradeScales = GradeScale::where('is_active', true)->orderBy('name')->get();
        $teachers = User::whereHas('teacher')->where('is_active', true)->orderBy('name')->get();

        return view('admin.assignments.edit', compact(
            'assignment',
            'academicYears',
            'academicTerms',
            'gradeCategories',
            'subjects',
            'classRooms',
            'gradeScales',
            'teachers'
        ));
    }

    public function update(Request $request, Assignment $assignment)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'assignment_code' => 'required|string|max:50|unique:assignments,assignment_code,' . $assignment->id,
            'description' => 'nullable|string',
            'instructions' => 'nullable|string',
            'academic_year_id' => 'required|exists:academic_years,id',
            'academic_term_id' => 'required|exists:academic_terms,id',
            'grade_category_id' => 'required|exists:grade_categories,id',
            'subject_id' => 'required|exists:subjects,id',
            'class_id' => 'required|exists:classes,id',
            'total_marks' => 'required|numeric|min:0',
            'passing_marks' => 'required|numeric|min:0|lte:total_marks',
            'assigned_date' => 'required|date',
            'due_date' => 'required|date|after_or_equal:assigned_date',
            'submission_type' => 'required|in:online,offline,both',
            'allow_late_submission' => 'boolean',
            'late_penalty_percentage' => 'nullable|numeric|min:0|max:100',
            'max_attempts' => 'nullable|integer|min:1',
            'is_published' => 'boolean',
            'status' => 'required|in:draft,active,completed,cancelled',
        ]);

        $assignment->update($validated);

        return redirect()->route('admin.grading.assignments.show', $assignment)
            ->with('success', 'Assignment updated successfully.');
    }

    public function destroy(Assignment $assignment)
    {
        // Check if assignment has submissions
        if ($assignment->submissions()->count() > 0) {
            return back()->with('error', 'Cannot delete assignment with existing submissions.');
        }

        $assignment->delete();

        return redirect()->route('admin.grading.assignments.index')
            ->with('success', 'Assignment deleted successfully.');
    }

    public function toggleStatus(Assignment $assignment)
    {
        $newStatus = $assignment->status === 'active' ? 'draft' : 'active';
        $assignment->update(['status' => $newStatus]);

        return back()->with('success', 'Assignment status updated successfully.');
    }

    public function togglePublished(Assignment $assignment)
    {
        $oldStatus = $assignment->is_published;
        $assignment->update(['is_published' => !$assignment->is_published]);

        $action = $assignment->is_published ? 'published' : 'unpublished';
        $undoAction = $oldStatus ? 'publish' : 'unpublish';

        return back()->with([
            'success' => "Assignment \"{$assignment->title}\" has been {$action} successfully.",
            'undo_action' => 'assignment_toggle_published',
            'undo_route' => route('admin.grading.assignments.undo'),
            'undo_data' => [
                'id' => $assignment->id,
                'status' => $oldStatus,
                'title' => $assignment->title,
                'action' => $undoAction
            ]
        ]);
    }

    public function duplicate(Assignment $assignment)
    {
        $newAssignment = $assignment->replicate();
        $newAssignment->assignment_code = $assignment->assignment_code . '_copy_' . time();
        $newAssignment->title = $assignment->title . ' (Copy)';
        $newAssignment->status = 'draft';
        $newAssignment->is_published = false;
        $newAssignment->save();

        return redirect()->route('admin.grading.assignments.edit', $newAssignment)
            ->with('success', 'Assignment duplicated successfully.');
    }

    public function undo(Request $request)
    {
        $action = $request->input('action');
        $data = $request->input('data');

        try {
            switch ($action) {
                case 'assignment_toggle_published':
                    $assignment = Assignment::findOrFail($data['id']);
                    $assignment->update(['is_published' => $data['status']]);
                    $actionText = $data['status'] ? 'published' : 'unpublished';
                    return response()->json([
                        'success' => true,
                        'message' => "Assignment \"{$data['title']}\" has been {$actionText} (undone)."
                    ]);
                    break;

                default:
                    return response()->json(['success' => false, 'message' => 'Unknown undo action.'], 400);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to undo action.'], 500);
        }
    }
}
